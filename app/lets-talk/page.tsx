"use client";

import React, { useState } from "react";
import "./LetsTalk.scss";
import { Box, Typography } from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import PhoneIcon from "@mui/icons-material/Phone";

const LetsTalk: React.FC = () => {
  // State to track the active tab
  const [activeTab, setActiveTab] = useState("I am a candidate");

  // Function to handle tab click
  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <Box className="get-in-touch-section">
      <Box className="get-in-touch-header">
        <Typography className="get-in-touch-title">
          Get in Touch With Us!
        </Typography>
      </Box>

      <Box className="contact-form-card">
        <Typography className="contact-us-title">Contact Us</Typography>

        <Box className="category-buttons">
          <button
            className={`category-button ${activeTab === "I am a candidate" ? "active" : ""}`}
            onClick={() => handleTabClick("I am a candidate")}
          >
            I am a candidate
          </button>
          <button
            className={`category-button ${activeTab === "I worked for Aadvik" ? "active" : ""}`}
            onClick={() => handleTabClick("I worked for Aadvik")}
          >
            I worked for Aadvik
          </button>
          <button
            className={`category-button ${activeTab === "I am a Client" ? "active" : ""}`}
            onClick={() => handleTabClick("I am a Client")}
          >
            I am a Client
          </button>
          <button
            className={`category-button ${activeTab === "I am a supplier" ? "active" : ""}`}
            onClick={() => handleTabClick("I am a supplier")}
          >
            I am a supplier
          </button>
        </Box>

        <form className="contact-form">
          <Box className="form-row">
            <Box className="form-group">
              <label htmlFor="firstName">*First Name</label>
              <input type="text" id="firstName" placeholder="" />
            </Box>
            <Box className="form-group">
              <label htmlFor="lastName">*Last Name</label>
              <input type="text" id="lastName" placeholder="" />
            </Box>
          </Box>

          <Box className="form-row">
            <Box className="form-group">
              <label htmlFor="email">*Email</label>
              <input type="email" id="email" placeholder="" />
            </Box>
            <Box className="form-group">
              <label htmlFor="country">*Country</label>
              <input type="text" id="country" placeholder="" />
            </Box>
          </Box>

          <Box className="form-row">
            <Box className="form-group">
              <label htmlFor="industry">*Industry</label>
              <input type="text" id="industry" placeholder="" />
            </Box>
            <Box className="form-group">
              <label htmlFor="company">*Company</label>
              <input type="text" id="company" placeholder="" />
            </Box>
          </Box>

          <Box className="form-row">
            <Box className="form-group">
              <label htmlFor="position">Position</label>
              <input type="text" id="position" placeholder="" />
            </Box>
            <Box className="form-group">
              <label htmlFor="phoneNo">Phone No.</label>
              <input type="tel" id="phoneNo" placeholder="" />
            </Box>
          </Box>

          <Box className="form-group message-group">
            <label htmlFor="message">*Message</label>
            <textarea
              id="message"
              placeholder=""
              rows={6}
              maxLength={500}
            ></textarea>
            <span className="char-count">0/500</span>
          </Box>

          <Box className="checkbox-group">
            <input type="checkbox" id="termsAgree" />
            <label htmlFor="termsAgree">
              I have read and agreed with <a href="#">Terms of use</a>,{" "}
              <a href="#">Privacy Policy</a> and <a href="#">Cookie Policy</a>.
            </label>
          </Box>

          <Box className="checkbox-group">
            <input type="checkbox" id="newsletterAgree" />
            <label htmlFor="newsletterAgree">
              Yes, please keep me updated on Aadvik news letter, events, offers
              of services and marketing activity by post, email, sms, MMS,
              phone, social media, push notifications in Apps and other means. I
              understand that I may opt out at any time.
            </label>
          </Box>

          <button type="submit" className="submit-message-button">
            Submit the Message
          </button>
        </form>
      </Box>

      <Box className="contact-info-section">
        <Box className="contact-info-card">
          <Box className="location-header">
            <LocationOnIcon className="icon-placeholder" />
            <Typography variant="h3">Canada</Typography>
          </Box>
          <Typography>No 33 Kilkarrin Rd, Brampton</Typography>
          <Typography>Ontario L7A4C6</Typography>
          <Box className="phone-number">
            <PhoneIcon className="icon-placeholder" />
            <Typography>Tel +91-8077052301</Typography>
          </Box>
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </Box>

        <Box className="contact-info-card">
          <Box className="location-header">
            <LocationOnIcon className="icon-placeholder" />
            <Typography variant="h3">India</Typography>
          </Box>
          <Typography>#SF04 Mithra Enclave, Doddakalashandra</Typography>
          <Typography>Bangalore 560062</Typography>
          <Box className="phone-number">
            <PhoneIcon className="icon-placeholder" />
            <Typography>Tel +91-8077052301</Typography>
          </Box>
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </Box>
      </Box>
    </Box>
  );
};

export default LetsTalk;