import { Box, List, ListItem, Typography } from "@mui/material";
import "./MechanicalDesignPage.scss";
import {
  mechanical_design_img1,
  mechanical_design_img2,
  mechanical_design_img3,
  mechanical_design_img4,
  mechanical_design_img5,
} from "@/public/index";
import Image from "next/image";
import { MechanicalDesignContent } from "@/constant";

const MechanicalDesignPage = () => {
  return (
    <Box className="mechanical-design-page-container">
      <Box className="mechanical-design-page-content">
        <Box className="mechanical-design-header-image" data-aos="fade-down">
          <Image
            src={mechanical_design_img1}
            alt="Mechanical Design Header"
            className="mechanical-design-header-img"
          />
        </Box>

        <Box className="mechanical-design-content">
          <Typography
            variant="body1"
            className="mechanical-design-description"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            {MechanicalDesignContent.mainDescription}
          </Typography>

          <List className="mechanical-design-list">
            {MechanicalDesignContent.materialTestingList.map((item, index) => (
              <ListItem
                key={index}
                className="mechanical-design-list-item"
                data-aos="fade-up"
                data-aos-duration="500"
                data-aos-delay={index * 100}
              >
                {item}
              </ListItem>
            ))}
          </List>

          <Box
            className="mechanical-design-flowchart"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            <Image
              src={mechanical_design_img2}
              alt="Mechanical Design Flowchart"
              className="mechanical-design-flowchart-img"
            />
          </Box>

          <Box className="tool-design-development">
            <Box className="tool-design-container">
              <Box className="tool-design-content">
                <Typography
                  variant="h2"
                  className="tool-design-title"
                  data-aos="fade-up"
                  data-aos-duration="500"
                >
                  {MechanicalDesignContent.toolDesign.title}
                </Typography>

                <Typography
                  variant="body1"
                  className="tool-design-description"
                  data-aos="fade-up"
                  data-aos-duration="500"
                >
                  {MechanicalDesignContent.toolDesign.description}
                </Typography>

                <List className="tool-design-list">
                  {MechanicalDesignContent.toolDesign.list.map((item, index) => (
                    <ListItem
                      key={index}
                      className="tool-design-list-item"
                      data-aos="fade-up"
                      data-aos-duration="500"
                      data-aos-delay={index * 100}
                    >
                      {item}
                    </ListItem>
                  ))}
                </List>
              </Box>

              <Box className="tool-design-images">
                <Image
                  src={mechanical_design_img3}
                  alt="Tool Design and Development"
                  className="tool-design-img"
                  data-aos="fade-up"
                  data-aos-duration="500"
                />
              </Box>
            </Box>

            <Box className="simulation-analysis">
              <Typography
                variant="h2"
                className="simulation-analysis-title"
                data-aos="fade-up"
                data-aos-duration="500"
              >
                {MechanicalDesignContent.simulationAnalysis.title}
              </Typography>
              {MechanicalDesignContent.simulationAnalysis.description.map(
                (text, index) => (
                  <Typography
                    key={index}
                    variant="body1"
                    className="simulation-analysis-description"
                    data-aos="fade-up"
                    data-aos-duration="500"
                  >
                    {text}
                  </Typography>
                )
              )}
              <Box
                className="simulation-analysis-images"
                data-aos="fade-up"
                data-aos-duration="500"
              >
                <Image
                  src={mechanical_design_img4}
                  alt="Simulation Analysis"
                  className="simulation-analysis-img"
                />

                <Typography
                  variant="h3"
                  className="simulation-analysis-subtitle"
                  data-aos="fade-up"
                  data-aos-duration="500"
                >
                  {MechanicalDesignContent.simulationAnalysis.subtitle}
                </Typography>
              </Box>

              <Box className="simulation-analysis-description-box">
                <Typography
                  variant="body1"
                  className="simulation-analysis-description"
                  data-aos="fade-up"
                  data-aos-duration="500"
                >
                  {
                    MechanicalDesignContent.simulationAnalysis
                      .additionalDescription
                  }
                </Typography>
                <Box className="simulation-analysis-testing">
                  {MechanicalDesignContent.simulationAnalysis.testing.map(
                    (test, index) => (
                      <Box key={index} sx={{ width: "100%" }}>
                        <Typography
                          className="simulation-analysis-testing-title"
                          data-aos="fade-up"
                          data-aos-duration="500"
                        >
                          {test.title}
                        </Typography>
                        <Typography
                          variant="body1"
                          className="simulation-analysis-description"
                          data-aos="fade-up"
                          data-aos-duration="500"
                        >
                          {test.description}
                        </Typography>
                      </Box>
                    )
                  )}
                </Box>
              </Box>
            </Box>

            <Box className="how-it-works">
              <Typography
                variant="h2"
                className="how-it-works-title"
                data-aos="fade-up"
                data-aos-duration="500"
              >
                {MechanicalDesignContent.howItWorks.title}
              </Typography>

              <Image
                src={mechanical_design_img5}
                alt="How It Works"
                className="how-it-works-img"
                data-aos="fade-up"
                data-aos-duration="500"
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default MechanicalDesignPage;
